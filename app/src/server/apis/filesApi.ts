import { Prisma, BookStatus } from "@prisma/client";
import { FilesExist, GetFiles } from "wasp/server/api";
import { MiddlewareConfigFn } from "wasp/server";

export const apiMiddleware: MiddlewareConfigFn = (config) => {
  return config;
};

export const filesExist: FilesExist = async (req, res, context) => {
  try {
    if (!context.user) {
      return res.status(401).json({
        error: "Authentication required",
      });
    }

    const file = await context.entities.File.findFirst({
      where: {
        userId: context.user.id,
      },
    });

    return res.status(200).json({
      hasFiles: !!file,
    });
  } catch (error) {
    return res.status(500).json({ message: (error as Error).message });
  }
};

export const getFiles: GetFiles = async (req, res, context) => {
  try {
    if (!context.user) {
      return res.status(401).json({
        error: "Authentication required",
      });
    }

    const { page = 1, pageSize = 20, status = "", search = "" } = req.query;

    // Validate and parse pagination parameters
    const parsedPage = parseInt(page as string, 10);
    const parsedPageSize = parseInt(pageSize as string, 10);
    const skip = (parsedPage - 1) * parsedPageSize;

    // Build where clause
    const where: Prisma.FileWhereInput = {
      userId: context.user.id,
      ...(status && { status: status as BookStatus }),
      ...(search && {
        name: { contains: search as string, mode: "insensitive" },
      }),
    };

    // Get total count and files in parallel
    const [files, totalCount] = await Promise.all([
      context.entities.File.findMany({
        take: parsedPageSize,
        skip,
        where,
        orderBy: { createdAt: "desc" },
      }),
      context.entities.File.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / parsedPageSize);

    return res.status(200).json({
      files,
      pagination: {
        page: parsedPage,
        pageSize: parsedPageSize,
        totalCount,
        totalPages,
        hasNextPage: parsedPage < totalPages,
        hasPreviousPage: parsedPage > 1,
      },
    });
  } catch (error) {
    return res.status(500).json({ message: (error as Error).message });
  }
};
