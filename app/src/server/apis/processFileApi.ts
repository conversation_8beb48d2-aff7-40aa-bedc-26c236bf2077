import { Prisma, PrismaClient } from "@prisma/client";
import { ProcessFile } from "wasp/server/api";
import { MiddlewareConfigFn } from "wasp/server";
import fs from "fs/promises";
import { createWriteStream } from "fs";
import path from "path";
import * as pdfjsLib from "pdfjs-dist";
import EPub from "epub";
import { fileTypeFromFile } from "file-type";
import { GoogleGenerativeAI } from "@google/generative-ai";
import PDFDocument from "pdfkit";
import { broadcastFileStatusUpdate } from "../utils/websocketUtils";

const prisma = new PrismaClient();
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "");
const OUTPUT_DIR = "/tmp/tp-processed"; // Directory for processed files

// Ensure output directory exists
async function ensureOutputDirExists() {
  try {
    await fs.mkdir(OUTPUT_DIR, { recursive: true });
  } catch (error) {
    console.error("Error creating output directory:", error);
  }
}

// Extract text from PDF
async function extractTextFromPDF(filePath: string): Promise<string> {
  const data = new Uint8Array(await fs.readFile(filePath));
  const doc = await pdfjsLib.getDocument({ data }).promise;
  let fullText = "";

  for (let i = 1; i <= doc.numPages; i++) {
    const page = await doc.getPage(i);
    const content = await page.getTextContent();
    const pageText = content.items.map((item: any) => item.str).join(" ");
    fullText += pageText + "\n";
  }

  await doc.destroy();
  return fullText;
}

// Extract text from EPUB
async function extractTextFromEPUB(filePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const epub = new EPub(filePath);
    let fullText = "";

    epub.on("end", () => {
      let chaptersProcessed = 0;
      const totalChapters = epub.flow.length;

      epub.flow.forEach((chapter: any) => {
        epub.getChapter(chapter.id, (error: Error | null, text: string) => {
          if (error) {
            reject(error);
            return;
          }

          // Remove HTML tags
          const cleanText = text.replace(/<[^>]*>/g, " ");
          fullText += cleanText + "\n";

          chaptersProcessed++;
          if (chaptersProcessed === totalChapters) {
            resolve(fullText);
          }
        });
      });
    });

    epub.parse();
  });
}

// Chunk text to fit within Gemini's token limits
async function chunkText(
  text: string,
  maxTokens: number = 60000
): Promise<string[]> {
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
  const chunks: string[] = [];
  let currentChunk = "";

  // Split text into paragraphs
  const paragraphs = text.split(/\n\s*\n/);

  for (const paragraph of paragraphs) {
    // Check if adding this paragraph would exceed the token limit
    const testChunk = currentChunk + paragraph + "\n\n";
    const { totalTokens } = await model.countTokens(testChunk);

    if (totalTokens > maxTokens && currentChunk.length > 0) {
      // Current chunk is full, save it and start a new one
      chunks.push(currentChunk);
      currentChunk = paragraph + "\n\n";
    } else {
      // Add paragraph to current chunk
      currentChunk = testChunk;
    }
  }

  // Add the last chunk if it's not empty
  if (currentChunk.length > 0) {
    chunks.push(currentChunk);
  }

  return chunks;
}

// Define tone-specific prompts
const TONE_PROMPTS = {
  academic: `Rewrite this text in an academic tone. Use formal language, precise terminology, and scholarly structure. Maintain objectivity and include appropriate academic phrasing. Present information systematically and analytically:`,

  simplified: `Rewrite this text in simple, clear language. Use short sentences, common words, and straightforward explanations. Break down complex concepts into easy-to-understand parts:`,

  kindergarten: `Rewrite this text for young children (ages 5-6). Use very simple words, short sentences, and fun examples. Explain things like you're talking to a curious child:`,

  poetic: `Rewrite this text in a poetic style. Use beautiful, flowing language with rhythm and imagery. Include metaphors, descriptive phrases, and lyrical expressions while keeping the meaning clear:`,

  humorous: `Rewrite this text with humor and wit. Add funny observations, clever wordplay, and amusing analogies. Keep it entertaining while preserving the important information:`,

  philosophical: `Rewrite this text in a philosophical tone. Explore deeper meanings, ask thought-provoking questions, and connect ideas to broader concepts about life, existence, and human nature:`,

  sarcastic: `Rewrite this text with a sarcastic and witty tone. Use irony, clever remarks, and dry humor. Be entertaining and slightly cynical while still conveying the essential information:`,
};

// Process text with Gemini using tone-specific prompts
async function simplifyTextWithGemini(
  text: string,
  tone: string = "simplified"
): Promise<string> {
  const model = genAI.getGenerativeModel({ model: "gemini-2.5-flash" });

  // Get the appropriate prompt for the tone, fallback to simplified if tone not found
  const tonePrompt =
    TONE_PROMPTS[tone as keyof typeof TONE_PROMPTS] || TONE_PROMPTS.simplified;

  const fullPrompt = `${tonePrompt}

${text}

Important: Provide only the rewritten text. Do not include any introductory phrases, acknowledgments, or meta-commentary. Start directly with the rewritten content.`;

  const result = await model.generateContent(fullPrompt);
  return result.response.text();
}

export const apiMiddleware: MiddlewareConfigFn = (config) => {
  return config;
};

export const processFile: ProcessFile = async (req, res, context) => {
  try {
    if (!context.user) {
      return res.status(401).json({
        error: "Authentication required",
      });
    }

    if (!req.body) {
      return res.status(400).json({
        error: "No file uploaded",
      });
    }

    const file = await context.entities.File.findUnique({
      where: {
        id: req.body.fileId,
        userId: context.user.id,
      },
    });

    if (!file || file.status !== "PENDING") {
      return res.status(400).json({
        error: "Invalid file",
      });
    }

    await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
      // Update file status to PROCESSING
      const updatedFile = await tx.file.update({
        where: {
          id: req.body.fileId,
        },
        data: { status: "PROCESSING" },
        select: {
          id: true,
          status: true,
        },
      });

      await tx.user.update({
        where: {
          id: context.user!.id,
        },
        data: { credits: { decrement: req.body.requiredCredits } },
      });

      // Broadcast the status update
      broadcastFileStatusUpdate(context.user!.id, updatedFile);
    });

    // Start processing in the background
    processFileTask(
      context.entities.File,
      req.body.path,
      req.body.fileId,
      req.body.tone || "simplified"
    );

    return res.status(200).json({ message: "Processing started" });
  } catch (error) {
    return res.status(500).json({ message: (error as Error).message });
  }
};

async function processFileTask(
  fileModel: Prisma.FileDelegate,
  filePath: string,
  id: string,
  tone: string
) {
  try {
    console.log("Processing file:", filePath);

    // Ensure output directory exists
    await ensureOutputDirExists();

    // Generate output file paths
    const originalFileName = path.basename(filePath, path.extname(filePath));
    const timestamp = Date.now();
    const pdfOutputFileName = `${originalFileName}_${tone}_${timestamp}.pdf`;
    const pdfOutputFilePath = path.join(OUTPUT_DIR, pdfOutputFileName);

    // Determine file type
    const fileType = await fileTypeFromFile(filePath);

    // Extract text based on file type
    let extractedText = "";
    if (fileType?.mime === "application/pdf") {
      extractedText = await extractTextFromPDF(filePath);
    } else if (fileType?.mime === "application/epub+zip") {
      extractedText = await extractTextFromEPUB(filePath);
    } else {
      throw new Error("Unsupported file type");
    }

    console.log(`Extracted ${extractedText.length} characters of text`);

    // Chunk the text to fit within Gemini's limits
    const chunks = await chunkText(extractedText);
    console.log(`Split text into ${chunks.length} chunks`);

    // Create PDF document
    const doc = new PDFDocument({
      margin: 50,
      size: "A4",
    });

    // Pipe the PDF to a write stream
    const stream = createWriteStream(pdfOutputFilePath);
    doc.pipe(stream);

    // Add metadata and title
    doc.info.Title = `${originalFileName} (${tone})`;
    doc.info.Author = "BookAI";
    doc.info.Creator = "BookAI Text Processor";

    // Add a title page
    doc
      .fontSize(24)
      .font("Helvetica-Bold")
      .text(`${originalFileName}`, { align: "center" });

    doc.moveDown();
    doc
      .fontSize(14)
      .font("Helvetica-Oblique")
      .text(`Processed with ${tone} tone`, { align: "center" });

    doc.moveDown(2);
    doc
      .fontSize(12)
      .font("Helvetica")
      .text(`Generated on: ${new Date().toLocaleString()}`, {
        align: "center",
      });

    // Add a new page for content
    doc.addPage();

    // Process each chunk with Gemini and add to PDF
    try {
      for (let i = 0; i < chunks.length; i++) {
        console.log(`Processing chunk ${i + 1}/${chunks.length}`);
        const processedText = await simplifyTextWithGemini(chunks[i], tone);

        // Add the processed text to the PDF
        doc.fontSize(12).font("Helvetica").text(processedText, {
          align: "left",
          lineGap: 7,
        });

        // Add a page break between chunks if not the last chunk
        if (i < chunks.length - 1) {
          doc.addPage();
        }
      }
    } finally {
      // Finalize the PDF
      doc.end();

      // Wait for the PDF to be fully written
      await new Promise<void>((resolve, reject) => {
        stream.on("finish", () => resolve());
        stream.on("error", reject);
      });
    }

    console.log(`Processed text saved to PDF: ${pdfOutputFilePath}`);

    // Update the file record with the processed file path
    const updatedFile = await fileModel.update({
      where: { id },
      data: {
        status: "COMPLETED",
      },
      select: {
        id: true,
        status: true,
        userId: true,
      },
    });

    // Broadcast the status update
    broadcastFileStatusUpdate(updatedFile.userId, {
      id: updatedFile.id,
      status: updatedFile.status,
    });

    console.log(`File ${id} processed successfully`);
  } catch (error) {
    console.error("Error processing file:", (error as Error).message);

    // Update status to FAILED and broadcast
    const updatedFile = await fileModel.update({
      where: { id },
      data: {
        status: "FAILED",
      },
      select: {
        id: true,
        status: true,
        userId: true,
      },
    });

    // Broadcast the status update
    broadcastFileStatusUpdate(updatedFile.userId, {
      id: updatedFile.id,
      status: updatedFile.status,
    });
  }
}
