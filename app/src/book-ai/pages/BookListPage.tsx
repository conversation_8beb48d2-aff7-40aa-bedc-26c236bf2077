import { useEffect, useState } from "react";
import type { BookStatus } from "../../shared/types";
import { api } from "wasp/client/api";
import { File } from "wasp/entities";
import BookCard from "../components/BookCard";
import Filters from "../components/Filters";
import BookCardSkeleton from "../components/skeleton";
import { useSocket } from "wasp/client/webSocket";

export default function BookListPage() {
  const [status, setStatus] = useState<BookStatus | "ALL">("ALL");
  const [searchTerm, setSearchTerm] = useState("");
  const [files, setFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 20;

  // Connect to WebSocket for real-time updates
  const { socket } = useSocket();

  // Initial data fetch
  useEffect(() => {
    async function getFiles() {
      if (!loading) setLoading(true);

      try {
        const response = await api.get(
          `http://localhost:3001/api/files?page=${page}&pageSize=${pageSize}&status=${
            status !== "ALL" ? status : ""
          }&search=${searchTerm}`
        );

        setTotalPages(response.data.pagination.totalPages);
        setFiles(response.data.files);
      } catch (error) {
        console.error("Error fetching files:", error);
      } finally {
        setLoading(false);
      }
    }

    getFiles();
  }, [page, pageSize, status, searchTerm]);

  // Handle WebSocket messages
  useEffect(() => {
    if (!socket) return;

    // Function to handle incoming messages
    const handleMessage = (data: any) => {
      try {
        // Check if this is a file status update
        if (data.type === "FILE_STATUS_UPDATE") {
          // Update the file in our state
          setFiles((prevFiles) =>
            prevFiles.map((file) =>
              file.id === data.file.id
                ? { ...file, status: data.file.status }
                : file
            )
          );
          console.log(
            `File ${data.file.id} status updated to ${data.file.status}`
          );
        }
      } catch (error) {
        console.error("Error handling WebSocket message:", error);
      }
    };

    // Add message handler
    socket.on("message", handleMessage);

    // Clean up on unmount
    return () => {
      socket.off("message", handleMessage);
    };
  }, [socket]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen  p-8 ">
      <Filters
        status={status}
        setStatus={setStatus}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
      />

      {loading ? (
        <BookCardSkeleton
          count={pageSize}
          className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
        />
      ) : files.length === 0 ? (
        <div className="mt-16 text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            No books found
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Try adjusting your filters or upload a new book
          </p>
        </div>
      ) : (
        <div className="mt-8">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
            {files.map((file) => (
              <BookCard
                key={file.id}
                name={file.name}
                type={file.type}
                status={file.status}
                createdAt={new Date(file.createdAt)}
                file={file}
              />
            ))}
          </div>

          <div className="mt-10 flex items-center justify-end">
            <nav
              className="flex items-center space-x-2"
              aria-label="Pagination"
            >
              <button
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page === 1}
                className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:bg-boxdark dark:text-gray-300 dark:hover:bg-gray-700"
              >
                <span className="sr-only">Previous</span>
                <svg
                  className="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>

              {[...Array(totalPages)].map((_, i) => {
                const pageNumber = i + 1;
                return (
                  <button
                    key={pageNumber}
                    onClick={() => setPage(pageNumber)}
                    className={`inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium ${
                      pageNumber === page
                        ? "bg-yellow-500 text-white"
                        : "border border-gray-300 bg-white text-gray-500 hover:bg-gray-50 dark:border-gray-600 dark:bg-boxdark dark:text-gray-300 dark:hover:bg-gray-700"
                    }`}
                  >
                    {pageNumber}
                  </button>
                );
              })}

              <button
                onClick={() => setPage(page + 1)}
                disabled={page >= totalPages}
                className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:bg-boxdark dark:text-gray-300 dark:hover:bg-gray-700"
              >
                <span className="sr-only">Next</span>
                <svg
                  className="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      )}
    </div>
  );
}
