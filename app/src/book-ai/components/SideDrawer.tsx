import { Drawer } from "vaul";
import { cn } from "../../client/cn";
import { File } from "wasp/entities";

interface SideDrawer {
  className: String;
  children: string | JSX.Element | JSX.Element[];
  fileDetails: File;
  onDelete: (id: string) => {};
}

export default function SideDrawer({
  children,
  className,
  fileDetails,
  onDelete,
}: SideDrawer) {
  return (
    <Drawer.Root direction="right">
      <Drawer.Trigger
        className={cn(
          "relative flex h-10 flex-shrink-0 items-center justify-center gap-2 overflow-hidden rounded-full bg-white px-4 text-sm font-medium shadow-sm transition-all hover:bg-[#FAFAFA] dark:bg-[#161615] dark:hover:bg-[#1A1A19] dark:text-white",
          className
        )}
      >
        {children}
      </Drawer.Trigger>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40" />
        <Drawer.Content
          className="right-2 top-2 bottom-2 fixed z-50 outline-none w-[310px] flex"
          // The gap between the edge of the screen and the drawer is 8px in this case.
          style={
            { "--initial-transform": "calc(100% + 8px)" } as React.CSSProperties
          }
        >
          <div className="bg-white dark:bg-boxdark h-full w-full grow p-5 flex flex-col rounded-[16px] shadow-md">
            <div className="max-w-md mx-auto h-full flex flex-col">
              <Drawer.Title className="font-medium text-xl mb-6 text-gray-900 dark:text-white border-b pb-3">
                File Details
              </Drawer.Title>
              <Drawer.Description className="text-gray-600 dark:text-bodydark flex-1 flex flex-col items-start">
                <ul className="mb-6 space-y-3 w-full">
                  <li className="flex justify-between">
                    <span className="font-medium">Name:</span>
                    <span className="text-gray-500 dark:text-bodydark1 truncate max-w-[180px]">
                      {fileDetails.name}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span className="font-medium">Type:</span>
                    <span className="text-gray-500 dark:text-bodydark1">
                      {fileDetails.type}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span className="font-medium">Upload Date:</span>
                    <span className="text-gray-500 dark:text-bodydark1">
                      {new Date(fileDetails.createdAt).toLocaleDateString()}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span className="font-medium">Status:</span>
                    <span className="text-gray-500 dark:text-bodydark1">
                      {fileDetails.status}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span className="font-medium">Size:</span>
                    <span className="text-gray-500 dark:text-bodydark1">
                      {/* {Math.round(fileDetails.size / 1024)}  */}
                      KB
                    </span>
                  </li>
                </ul>

                <div className="flex flex-col gap-3 w-full mb-4">
                  <button className="w-full rounded-lg bg-primary px-4 py-2 text-white transition hover:bg-opacity-90 flex items-center justify-center">
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                      ></path>
                    </svg>
                    Download File
                  </button>

                  <button
                    onClick={() => onDelete(fileDetails.id)}
                    className="w-full rounded-lg border border-red-500 px-4 py-2 text-red-500 transition hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center justify-center"
                  >
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                      ></path>
                    </svg>
                    Delete File
                  </button>
                </div>

                <div className="mt-auto pt-4 border-t border-gray-200 dark:border-gray-700 text-sm text-gray-500 dark:text-gray-400 w-full">
                  <p className="font-medium mb-1">Note: </p>
                  <p>
                    Processing may take some time. Feel free to navigate the app
                    while waiting.
                  </p>
                </div>
              </Drawer.Description>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
